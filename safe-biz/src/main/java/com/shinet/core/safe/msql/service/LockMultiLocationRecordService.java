package com.shinet.core.safe.msql.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.safe.core.entity.LockMultiLocationRecord;
import com.shinet.core.safe.core.mapper.LockMultiLocationRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 锁区多位置记录表服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-24
 */
@Slf4j
@Service
public class LockMultiLocationRecordService extends ServiceImpl<LockMultiLocationRecordMapper, LockMultiLocationRecord> {

    /**
     * 根据设备信息查询记录
     * 利用唯一索引：device_id + product + os
     *
     * @param deviceId 设备ID
     * @param product 产品标识
     * @param os 操作系统
     * @return 记录对象，不存在返回null
     */
    public LockMultiLocationRecord getByDeviceInfo(String deviceId, String product, String os) {
        return baseMapper.selectByDeviceProductOs(deviceId, product, os);
    }

    /**
     * 保存或更新多位置记录
     * 如果记录已存在则更新，否则新增
     *
     * @param deviceId      设备ID
     * @param product       产品标识
     * @param os            操作系统
     * @param locationCount 位置数量
     * @param locationInfo  位置详情JSON
     */
    public void saveOrUpdate(String deviceId, String product, String os,
                             Integer locationCount, String locationInfo) {
        LockMultiLocationRecord existingRecord = getByDeviceInfo(deviceId, product, os);
        
        Date now = new Date();
        
        if (existingRecord != null) {
            // 更新现有记录
            existingRecord.setLocationCount(locationCount)
                         .setLocationInfo(locationInfo)
                         .setUpdateTime(now);
            updateById(existingRecord);
            log.info("更新多位置记录成功，设备ID: {}, 产品: {}, 系统: {}, 位置数量: {}", 
                    deviceId, product, os, locationCount);
        } else {
            // 新增记录
            LockMultiLocationRecord newRecord = new LockMultiLocationRecord()
                    .setBasicInfo(product, deviceId, os, locationCount, locationInfo)
                    .setCreateTime(now)
                    .setUpdateTime(now);
            save(newRecord);
            log.info("新增多位置记录成功，设备ID: {}, 产品: {}, 系统: {}, 位置数量: {}", 
                    deviceId, product, os, locationCount);
        }
    }

    /**
     * 根据产品和操作系统查询记录列表
     *
     * @param product 产品标识
     * @param os 操作系统
     * @return 记录列表
     */
    public List<LockMultiLocationRecord> listByProductAndOs(String product, String os) {
        return baseMapper.selectByProductAndOs(product, os);
    }

    /**
     * 根据位置数量范围查询记录
     *
     * @param minCount 最小位置数量
     * @param maxCount 最大位置数量
     * @return 记录列表
     */
    public List<LockMultiLocationRecord> listByLocationCountRange(Integer minCount, Integer maxCount) {
        return baseMapper.selectByLocationCountRange(minCount, maxCount);
    }

    /**
     * 统计指定产品和操作系统的记录数量
     *
     * @param product 产品标识
     * @param os 操作系统
     * @return 记录数量
     */
    public Long countByProductAndOs(String product, String os) {
        return baseMapper.countByProductAndOs(product, os);
    }

    /**
     * 批量保存记录
     *
     * @param records 记录列表
     * @return 保存成功的记录数
     */
    public int batchSave(List<LockMultiLocationRecord> records) {
        if (CollectionUtils.isEmpty(records)) {
            return 0;
        }
        
        Date now = new Date();
        records.forEach(record -> {
            if (record.getCreateTime() == null) {
                record.setCreateTime(now);
            }
            if (record.getUpdateTime() == null) {
                record.setUpdateTime(now);
            }
        });
        
        int result = baseMapper.batchInsert(records);
        log.info("批量保存多位置记录完成，总数: {}, 成功: {}", records.size(), result);
        return result;
    }

    /**
     * 根据设备ID列表批量查询
     *
     * @param deviceIds 设备ID列表
     * @param product 产品标识
     * @param os 操作系统
     * @return 记录列表
     */
    public List<LockMultiLocationRecord> listByDeviceIds(List<String> deviceIds, String product, String os) {
        if (CollectionUtils.isEmpty(deviceIds)) {
            return new ArrayList<>();
        }
        return baseMapper.selectByDeviceIds(deviceIds, product, os);
    }

    /**
     * 检查设备是否存在多位置记录
     *
     * @param deviceId 设备ID
     * @param product 产品标识
     * @param os 操作系统
     * @return true-存在记录，false-不存在
     */
    public boolean existsByDeviceInfo(String deviceId, String product, String os) {
        return getByDeviceInfo(deviceId, product, os) != null;
    }

    /**
     * 获取设备的位置数量
     *
     * @param deviceId 设备ID
     * @param product 产品标识
     * @param os 操作系统
     * @return 位置数量，不存在记录返回0
     */
    public Integer getLocationCount(String deviceId, String product, String os) {
        LockMultiLocationRecord record = getByDeviceInfo(deviceId, product, os);
        return record != null ? record.getLocationCount() : 0;
    }
}
