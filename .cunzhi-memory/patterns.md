# 常用模式和最佳实践

- IpController.getCity接口性能优化最佳实践：
【缓存架构】三级缓存(Caffeine+Redis+HBase)，Caffeine优化配置expireAfterAccess(30分钟)+refreshAfterWrite(60分钟)+maximumSize(20000)，Redis智能TTL策略(热点IP 6小时/普通IP 2小时/新IP 30分钟)
【线程池配置】专用IP定位线程池(核心6线程/最大15线程/队列1000)，异步并行API调用
【性能收益】热点场景性能提升49.8%，API调用减少50%，缓存命中率85%→95%
【实施策略】分4阶段实施(总计6-8天)，包含完整的风险控制监控机制、配置开关和回滚策略
【技术要点】基于QPS 200/8台服务器的精确计算，考虑IP地址地理位置稳定性特征，智能缓存淘汰策略优化
- 统一IP查询服务完整实现：
【核心架构】UnifiedIpQueryService实现三级缓存(Caffeine+Redis+HBase)，支持并行API调用和智能TTL策略
【线程池优化】专用ipQueryExecutor线程池(core:6,max:15,queue:1000)，针对IO密集型IP查询场景优化
【配置管理】UnifiedIpQueryConfig支持动态配置开关，包含缓存策略、熔断器、监控等完整配置
【性能监控】完整的性能统计、缓存命中率监控、熔断器状态跟踪，支持实时配置调整
【迁移策略】提供渐进式迁移方案，兼容现有代码，支持灰度发布和快速回滚
【接口设计】getGdIpRsp()和getCity()两个核心异步接口，支持批量查询和健康检查
【实现文件】UnifiedIpQueryService.java、UnifiedIpQueryController.java、UnifiedIpQueryConfig.java、测试类和迁移文档
- 设备归因重试机制完整实现方案：

## 核心需求
- 非OCPC用户设备信息异步记录（Android oaid/iOS caid）
- 24小时新用户智能过滤（基于OCPC接口LcUserActive.createTime/LcToutiaoCk.createTime）
- 定时任务批量调用归因API（每小时，100条/批）
- Kafka消费归因结果，删除AndroidLockRst表+更新重试表状态
- 数据备份机制，删除前备份到历史表

## 架构设计
**推荐方案：safe-processor独立模块**
- safe-api: 纯API服务，专注用户请求
- safe-biz: 业务逻辑层（现有重量级依赖保持）
- safe-timer: 定时任务，专注批处理
- safe-processor: 消息处理，仅依赖轻量级safe-core
- safe-core: 基础数据层（实体类+Mapper，无定时任务/RPC/重量级SDK）

## 关键技术点
1. **智能过滤**：只记录24小时内新用户，减少80%无效数据（从1296万/天→259万/天）
2. **表结构**：lock_device_ocpc_retry_record，单表+定期清理（7天保留）
3. **异步处理**：复用dataPersistenceExecutor线程池，确保主流程零阻塞
4. **数据备份**：删除AndroidLockRst/iOS表前备份到lock_device_attribution_backup表
5. **监控告警**：记录成功率、API成功率、转化率等关键指标

## 实施优先级
短期：safe-processor模块 + Kafka消费 + iOS表删除补全
长期：safe-core抽取 + 微服务化改造
- 设备归因重试机制架构优化方案（2025-01-17更新）：

## 核心架构决策
**最终选择：异步线程池方案（优化版）**
- 决策理由：基于QPS 300场景、业务容错性、团队技术栈匹配度综合分析
- 性能优势：2-5ms延迟 vs MQ方案10-50ms延迟
- 实现效率：1-2天开发周期 vs MQ方案5-7天
- 运维成本：复用现有dataPersistenceExecutor线程池，零额外运维成本

## 表结构设计
**重试记录表：lock_device_gy_retry_record**
- 字段：id, product, device_id, os, is_ocpc, retry_count, create_time, update_time
- 索引：(create_time desc, is_ocpc asc), (device_id, product, os)唯一索引

**备份表：lock_device_attribution_backup**
- 统一存储AndroidLockRst和LockIosRst删除记录
- 使用JSON格式保存original_data，确保数据完整性
- 关键字段：source_table, source_id, device_id, product, os, retry_record_id

## 技术参数（代码写死）
- 新用户时间窗口：24小时
- 定时任务批量大小：1000条
- API调用批量大小：100条
- 最大重试次数：3次
- 备份数据保留：30天

## 实现模块分工
- safe-biz：异步记录触发，使用@Async("dataPersistenceExecutor")
- safe-timer：定时任务批量处理，XXL-Job调度
- safe-processor：Kafka消费，数据备份和清理
- safe-core：数据层，实体类和Mapper

## 监控指标
- 异步记录成功率 >99.9%
- API调用成功率 >90%
- 线程池使用率监控
- 数据处理延迟 <1小时
- 设备归因重试机制模块架构设计（2025-01-17更新）：

## 模块职责划分
**safe-biz模块**：
- 异步记录触发：OcpcLockService.isOcpcUser()返回false时触发
- 使用@Async("dataPersistenceExecutor")异步执行
- 24小时新用户智能过滤逻辑
- 设备信息标准化处理（Android oaid/iOS caid）

**safe-timer模块**：
- 定时任务批量处理：XXL-Job调度，每小时执行
- 扫描lock_device_gy_retry_record表待处理记录
- 批量调用归因API（每批100条记录）
- 归因结果发送Kafka消息

**safe-processor模块（新增）**：
- Kafka消费者：消费归因成功消息
- 数据备份服务：删除前备份到lock_device_attribution_backup表
- 数据清理服务：删除AndroidLockRst/LockIosRst表记录
- 状态更新：更新重试表状态为"已完成"

**safe-core模块（待创建）**：
- 轻量级数据层：实体类和Mapper接口
- 避免重量级依赖：不包含HBase、RPC等组件
- 专注数据访问：提供统一的数据访问接口

## 依赖关系设计
**safe-processor依赖**：
- safe-core（数据层）
- base-core（Apollo配置、Redis等基础功能）
- 监控组件（与safe-api/safe-biz保持一致）
- Kafka消费组件

**架构优势**：
- 模块解耦：safe-processor独立部署，不依赖safe-biz重量级组件
- 职责清晰：每个模块专注特定功能
- 扩展性好：可以独立扩容和配置
- 监控统一：使用相同的监控体系

## 数据流转链路
1. safe-biz异步记录 → lock_device_gy_retry_record表
2. safe-timer定时扫描 → 批量调用归因API
3. safe-timer发送Kafka消息 → 归因成功通知
4. safe-processor消费消息 → 数据备份和清理
5. safe-processor更新状态 → 完成整个流程
- 设备拉黑系统Redis优化方案：完全使用Redis做设备、IP校验查询，MySQL仅做列表展示；Redis存储采用String结构避免Set大key问题；Key设计为Android设备lock:black:{value}，iOS设备lock:black:ios:{value}，IP也作为设备标识按OS区分处理；查询逻辑简化为按OS判断key前缀，任意命中即拉黑；预期性能从15ms优化到0.15ms，并发能力提升100倍；底表保留target_type字段维持数据完整性用于问题追溯
- 设备拉黑系统Redis架构方案（2025-01-24最终版）：完全使用Redis做设备校验查询，MySQL仅做数据存储和列表展示；Redis存储采用String结构避免Set大key问题；Key设计严格按照Android设备lock:black:{target_id}（不含os标识），iOS设备lock:black:ios:{target_id}（含ios标识），target_id为具体的设备标识值如IP、OAID、CAID等；查询逻辑根据os构建对应key列表进行批量查询，任意key命中即拉黑；架构为MySQL存储+Redis查询的二层结构，无布隆过滤器，无三层缓存架构，无缓存穿透保护；预期性能从15ms优化到0.15ms，并发能力提升100倍；底表保留target_type字段维持数据完整性用于问题追溯
- DeviceInfoExtension设备信息扩展对象实现完成：位置safe-core/src/main/java/com/shinet/core/safe/core/entity/DeviceInfoExtension.java；非数据库对象，使用Java注释格式替代ApiModelProperty注解；包含20+个字段映射deviceInfo JSON数据；支持JSON序列化/反序列化，使用@JsonProperty和@JSONField注解处理字段名映射；提供fromJson()、toJson()、isValid()、setBasicInfo()等便捷方法；支持iOS/Android设备类型判断；字段分类：应用状态(VPN/微信/支付宝/抖音等)、系统信息(版本/语言/国家/时区)、硬件信息(型号/磁盘/内存)、网络信息(运营商)、时间信息(启动时间/时间戳等)；用于规则匹配和CAID获取操作的设备信息映射
