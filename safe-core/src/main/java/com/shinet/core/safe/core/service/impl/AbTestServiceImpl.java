package com.shinet.core.safe.core.service.impl;

import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson.JSON;
import com.shinet.core.safe.core.client.AbTestHttpClient;
import com.shinet.core.safe.core.constants.AbTestConstants;
import com.shinet.core.safe.core.dto.AbTestRequestDTO;
import com.shinet.core.safe.core.service.AbTestService;
import com.shinet.core.safe.core.vo.AbTestResponseVO;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;

/**
 * AB实验服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AbTestServiceImpl implements AbTestService {

    @Autowired
    private AbTestHttpClient abTestHttpClient;

    @Resource(name = "redissonClient2")
    private RedissonClient redissonClient;


    @Override
    public Integer getAbTestCategoryId(AbTestRequestDTO request) {
        try {
            String cacheKey = AbTestConstants.CACHE_PREFIX_CATEGORY + generateCacheKey(request);
            RBucket<Integer> bucket = redissonClient.getBucket(cacheKey);

            // 尝试从缓存获取
            Integer cachedCategoryId = bucket.get();
            if (cachedCategoryId != null) {
                return cachedCategoryId;
            }

            // 缓存未命中或值为0，调用远程服务
            AbTestResponseVO response = abTestHttpClient.callAbTestService(request);
            Integer categoryId = response.getData().getCategoryId();

            if (categoryId == null) {
                categoryId = AbTestConstants.DEFAULT_CATEGORY_ID;
            }

            bucket.set(categoryId, Duration.ofSeconds(AbTestConstants.CACHE_TTL_SECONDS));

            return categoryId;

        } catch (Exception e) {
            log.error("获取AB实验分组ID异常，返回默认分组", e);
            return AbTestConstants.DEFAULT_CATEGORY_ID;
        }
    }

    @Override
    public AbTestResponseVO getAbTestResponse(AbTestRequestDTO request) {
        try {
            String cacheKey = AbTestConstants.CACHE_PREFIX_RESPONSE + generateCacheKey(request);
            RBucket<AbTestResponseVO> bucket = redissonClient.getBucket(cacheKey);

            // 尝试从缓存获取
            AbTestResponseVO cachedResponse = bucket.get();
            if (cachedResponse != null && cachedResponse.getData().getCategoryId() != null && cachedResponse.getData().getCategoryId() != 0) {
                return cachedResponse;
            }

            // 缓存未命中或值为0，调用远程服务
            AbTestResponseVO response = abTestHttpClient.callAbTestService(request);

            if (response == null) {
                response = AbTestResponseVO.createDefault();
            }

            if (response.getData().getCategoryId() != null && response.getData().getCategoryId() != 0) {
                bucket.set(response, Duration.ofSeconds(AbTestConstants.CACHE_TTL_SECONDS));
            }

            return response;

        } catch (Exception e) {
            log.error("获取AB实验响应异常，返回默认响应", e);
            return AbTestResponseVO.createDefault();
        }
    }

    /**
     * 生成缓存Key
     * 基于请求参数MD5生成
     *
     * @param request 请求参数
     * @return 缓存Key
     */
    private String generateCacheKey(AbTestRequestDTO request) {
        try {
            String requestJson = JSON.toJSONString(request);
            return MD5.create().digestHex(requestJson);
        } catch (Exception e) {
            log.error("生成缓存Key异常", e);
            return request.getDeviceId() + ":" + request.getProduct();
        }
    }


}
