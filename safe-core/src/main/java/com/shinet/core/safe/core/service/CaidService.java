package com.shinet.core.safe.core.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson.JSON;
import com.shinet.core.safe.core.client.CaidHttpClient;
import com.shinet.core.safe.core.dto.CaidRequestDTO;
import com.shinet.core.safe.core.vo.CaidResponseVO;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;

/**
 * CAID获取服务
 * 
 */
@Slf4j
@Service
public class CaidService {
    
    // 缓存前缀
    private static final String CACHE_PREFIX = "lock:get:caid:";
    
    // 缓存TTL（3天 = 259200秒）
    private static final int CACHE_TTL_SECONDS = 259200;
    
    @Resource
    private CaidHttpClient caidHttpClient;
    
    @Autowired
    @Qualifier("redissonClient2")
    private RedissonClient redissonClient;
    
    /**
     * 获取CAID
     * 
     * @param request 请求参数
     * @return CAID值，异常或无数据时返回null
     */
    public String getCaid(CaidRequestDTO request) {
        try {

            if (StrUtil.isNotBlank(request.getCarrierInfo())
                    && request.getCarrierInfo().contains("--")){
                // 改为默认值，caid接口会处理
                request.setCarrierInfo("--");
            }

            String cacheKey = CACHE_PREFIX + generateCacheKey(request);
            RBucket<String> bucket = redissonClient.getBucket(cacheKey);

            // 尝试从缓存获取
            String cachedCaid = bucket.get();
            if (cachedCaid != null) {
                return cachedCaid;
            }

            // 缓存未命中，调用远程服务
            CaidResponseVO response = caidHttpClient.callCaidService(request);
            if (response != null 
                    && 200 == response.getStatus()
                    && response.getData() != null 
                    && StrUtil.isNotBlank(response.getData())) {
                
                String caid = response.getData();
                
                // 缓存结果
                bucket.set(caid, Duration.ofSeconds(CACHE_TTL_SECONDS));
                
                return caid;
            }

            return null;

        } catch (Exception e) {
            log.error("获取CAID异常", e);
            return null;
        }
    }
    
    /**
     * 生成缓存Key
     * 基于请求参数MD5生成
     *
     * @param request 请求参数
     * @return 缓存Key
     */
    private String generateCacheKey(CaidRequestDTO request) {
        try {
            String requestJson = JSON.toJSONString(request);
            return MD5.create().digestHex(requestJson);
        } catch (Exception e) {
            log.error("生成缓存Key异常", e);
            return request.getDeviceName() + ":" + request.getProduct();
        }
    }
}
