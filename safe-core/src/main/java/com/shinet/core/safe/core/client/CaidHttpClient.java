package com.shinet.core.safe.core.client;

import com.alibaba.fastjson.JSON;
import com.shinet.core.safe.core.dto.CaidRequestDTO;
import com.shinet.core.safe.core.vo.CaidResponseVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.net.URI;
import java.nio.charset.StandardCharsets;

/**
 * CAID获取HTTP客户端
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class CaidHttpClient {
    
    // HTTP超时配置（毫秒）
    private static final int HTTP_CONNECT_TIMEOUT = 1000;
    private static final int HTTP_SOCKET_TIMEOUT = 1000;
    private static final int HTTP_REQUEST_TIMEOUT = 500;
    
    // 连接池配置
    private static final int HTTP_MAX_TOTAL = 50;
    private static final int HTTP_MAX_PER_ROUTE = 20;
    
    @Value("${caid.service.url:}")
    private String caidServiceUrl;
    
    private CloseableHttpClient httpClient;
    private PoolingHttpClientConnectionManager connectionManager;
    
    @PostConstruct
    public void init() {
        connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(HTTP_MAX_TOTAL);
        connectionManager.setDefaultMaxPerRoute(HTTP_MAX_PER_ROUTE);

        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(HTTP_CONNECT_TIMEOUT)
                .setSocketTimeout(HTTP_SOCKET_TIMEOUT)
                .setConnectionRequestTimeout(HTTP_REQUEST_TIMEOUT)
                .build();
        
        httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(requestConfig)
                .build();
        
        log.info("CAID HTTP客户端初始化完成，服务地址: {}", caidServiceUrl);
    }
    
    @PreDestroy
    public void destroy() {
        try {
            if (httpClient != null) {
                httpClient.close();
            }
            if (connectionManager != null) {
                connectionManager.close();
            }
            log.info("CAID HTTP客户端已关闭");
        } catch (Exception e) {
            log.error("关闭CAID HTTP客户端异常", e);
        }
    }
    
    /**
     * 调用CAID获取服务
     * 
     * @param request 请求参数
     * @return CAID响应，异常时返回null
     */
    public CaidResponseVO callCaidService(CaidRequestDTO request) {
        if (caidServiceUrl == null || caidServiceUrl.trim().isEmpty()) {
            log.warn("CAID服务URL未配置，返回null");
            return null;
        }
        
        try {
            // 构建GET请求URL参数
            URIBuilder uriBuilder = new URIBuilder(caidServiceUrl);
            uriBuilder.addParameter("bootTimeInSec", request.getBootTimeInSec());
            uriBuilder.addParameter("carrierInfo", request.getCarrierInfo());
            uriBuilder.addParameter("countryCode", request.getCountryCode());
            uriBuilder.addParameter("deviceName", request.getDeviceName());
            uriBuilder.addParameter("disk", request.getDisk());
            uriBuilder.addParameter("language", request.getLanguage());
            uriBuilder.addParameter("machine", request.getMachine());
            uriBuilder.addParameter("memory", request.getMemory());
            uriBuilder.addParameter("modelF", request.getModelF());
            uriBuilder.addParameter("product", request.getProduct());
            uriBuilder.addParameter("sysFileTime", request.getSysFileTime());
            uriBuilder.addParameter("systemVersion", request.getSystemVersion());
            uriBuilder.addParameter("timeZone", request.getTimeZone());
            uriBuilder.addParameter("userId", request.getUserId());
            
            URI uri = uriBuilder.build();
            HttpGet httpGet = new HttpGet(uri);
            
            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                
                if (response.getStatusLine().getStatusCode() == 200) {
                    CaidResponseVO result = JSON.parseObject(responseBody, CaidResponseVO.class);
                    if (result != null
                            && 200 == result.getStatus()
                            && result.getData() != null
                            && !result.getData().trim().isEmpty()) {
                        return result;
                    }
                }
                
                log.warn("CAID服务响应异常，状态码: {}, 响应: {}", 
                        response.getStatusLine().getStatusCode(), responseBody);
                
            }
        } catch (Exception e) {
            log.error("调用CAID服务异常", e);
        }
        
        return null;
    }
}
