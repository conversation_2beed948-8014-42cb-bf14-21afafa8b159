package com.shinet.core.safe.core.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * CAID获取请求参数DTO
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CaidRequestDTO {
    
    /**
     * 启动时间（秒）
     */
    private String bootTimeInSec;
    
    /**
     * 运营商信息
     */
    private String carrierInfo;
    
    /**
     * 国家代码
     */
    private String countryCode;
    
    /**
     * 设备名称
     */
    private String deviceName;
    
    /**
     * 磁盘容量
     */
    private String disk;
    
    /**
     * 语言设置
     */
    private String language;
    
    /**
     * 机器型号
     */
    private String machine;
    
    /**
     * 内存大小
     */
    private String memory;
    
    /**
     * 型号标识
     */
    private String modelF;
    
    /**
     * 产品标识
     */
    private String product;
    
    /**
     * 系统文件时间
     */
    private String sysFileTime;
    
    /**
     * 系统版本
     */
    private String systemVersion;
    
    /**
     * 时区
     */
    private String timeZone;
    
    /**
     * 用户ID
     */
    private String userId;
}
