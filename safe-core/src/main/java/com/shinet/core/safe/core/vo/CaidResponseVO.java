package com.shinet.core.safe.core.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * CAID获取响应VO
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CaidResponseVO {

    /**
     * 响应数据
     */
    private String data;
    
    /**
     * 响应状态码
     */
    private int status;
    
    /**
     * 响应消息
     */
    private String message;
}
