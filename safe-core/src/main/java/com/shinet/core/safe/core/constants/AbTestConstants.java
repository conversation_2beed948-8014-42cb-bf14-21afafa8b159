package com.shinet.core.safe.core.constants;

/**
 * AB实验常量类
 * 
 * <AUTHOR>
 */
public class AbTestConstants {
    
    /**
     * 缓存前缀
     */
    public static final String CACHE_PREFIX_CATEGORY = "abtest:category:";
    public static final String CACHE_PREFIX_RESPONSE = "abtest:response:";
    
    /**
     * 缓存TTL（秒）
     */
    public static final int CACHE_TTL_SECONDS = 300;
    public static final int GROUP_0CACHE_TTL_SECONDS = 120;

    /**
     * 默认分组ID
     */
    public static final Integer DEFAULT_CATEGORY_ID = 0;
    
    /**
     * HTTP超时配置（毫秒）
     */
    public static final int HTTP_CONNECT_TIMEOUT = 1000;
    public static final int HTTP_SOCKET_TIMEOUT = 1000;
    public static final int HTTP_REQUEST_TIMEOUT = 500;
    
    /**
     * 连接池配置
     */
    public static final int HTTP_MAX_TOTAL = 50;
    public static final int HTTP_MAX_PER_ROUTE = 20;
    
    /**
     * 默认SDK版本
     */
    public static final String DEFAULT_SDK_VERSION = "1.0";
}
